const { PDFDocument } = require('pdf-lib');

const docRepoModel = require('@models/docRepoModel');
const config = require('@config/config');

const printDocs = async (req, res, next) => {
  global.logger.debug('resolvers/doc-repo/printDocs');
  global.logger.debug('with params: \n%o', {
    user: req.user,
    ...req.params,
  });

  const { department, id } = req.params;
  try {
    let bufferPdf = [];
    let isPdfBlank = false;
    const dealId = id.slice(0, 15);

    if (department === '000500') {
      const pdfs = await getWXDocs(dealId);
      bufferPdf = pdfs.mergedPdfs;
      isPdfBlank = pdfs.isPdfBlank;
    }

    res.status(200).json({ bufferPdf, isPdfBlank });
  } catch (error) {
    return next(error);
  }
};

// TODO: When adding document printing for other business types, we should be thinking about how to break out
// the common logic in this function.
// Same code copied from Scheduler 1 and only created functions for S3 operations in docRepoModel
const getWXDocs = async (dealId) => {
  const includePdfDocs = ['BAS', 'PV'];
  let s3Params = {
    Bucket: config.sch1DocRepoPath,
    Prefix: `${dealId}/`,
  };
  // getting object list for post qc docs pdf is uploaded with different naming standards
  const s3ObjectList = await docRepoModel.getObjectListByBucket(s3Params);
  // docPath Example: dealId/documentName.pdf
  const s3Docs = s3ObjectList.map(({ Key: docPath }) => {
    const doc = docPath.split('/').pop();
    // eslint-disable-next-line no-unused-vars
    const [name, extension] = doc.split('.');
    return name;
  });
  const filteredPdfDocs = includePdfDocs.filter((pdf) => {
    return s3Docs.indexOf(pdf) > -1;
  });
  // filtering the wx docs from all uploaded docs
  const wxDocs = s3Docs.filter((pdf) => {
    const pdfLowerCase = pdf.toLowerCase();
    // TODO: need to find a better way to do this.
    if (!pdfLowerCase.includes('wxdocs')) return false;
    if (pdfLowerCase.includes('deleted') || pdfLowerCase.includes('pre')) return false;
    if (pdfLowerCase.includes('post')) return true;
    return true;
  });
  s3Params = { Bucket: `${config.sch1DocRepoPath}/${dealId}`, Key: '' };
  // finding correct qc wx docs
  // There are multiple versions of wx docs and we require to grab the latest.
  if (wxDocs.length === 1) {
    filteredPdfDocs.push(wxDocs);
  } else if (wxDocs.length !== 0) {
    for (let i = 0; i < wxDocs.length; i++) {
      s3Params.Key = `${wxDocs[i]}.pdf`;
      const objMetadata = await docRepoModel.getObjectDetails(s3Params).promise();
      const { LastModified: wxDocsUploadDate } = objMetadata;
      wxDocs[i] = { name: wxDocs[i], date: wxDocsUploadDate };
    }
    wxDocs.sort((acc, curr) => {
      return new Date(curr.date) - new Date(acc.date);
    });
    filteredPdfDocs.push(wxDocs[0].name);
  }
  const listBufferPdfs = [];
  // get pdf file from s3 for filtered docs
  for (let i = 0; i < filteredPdfDocs.length; i++) {
    s3Params.Key = `${filteredPdfDocs[i]}.pdf`;
    const { Body: bufferData } = await docRepoModel.downloadFile(s3Params);
    listBufferPdfs.push(Buffer.from(bufferData));
  }
  // Merge all buffered pdfs to a single buffer pdf
  const pdfs = await PDFDocument.create();
  for (const pdfBytes of listBufferPdfs) {
    const pdf = await PDFDocument.load(pdfBytes);
    const copiedPages = await pdfs.copyPages(pdf, pdf.getPageIndices());
    copiedPages.forEach((page) => {
      pdfs.addPage(page);
    });
  }
  const mergedPdfs = await pdfs.save();
  return { mergedPdfs, isPdfBlank: filteredPdfDocs.length === 0 };
};

module.exports = printDocs;
