export HOME="/home/<USER>"
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"

cd /home/<USER>/scheduler2-backend
cp -t ./../build/ -a src/views/.

cd /home/<USER>/build
npm install --only=production
pm2 restart all

# Below is script if needed to run for the very first time, pm2 remembers previous scripts so restarting should be enough
# pm2 start build/index.js --name scheduler2 --output logs/out.log --error logs/err.log --time

