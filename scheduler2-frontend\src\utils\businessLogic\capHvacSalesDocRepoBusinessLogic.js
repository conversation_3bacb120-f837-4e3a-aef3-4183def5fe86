const resultingRequiredDocs = {
  'HVAC_Sales-CAP': {
    required: ['Contract', 'PV', 'Man J Full', 'Man J 1st', 'PICS', 'WS'],
    displaceGasHeatWithHeatPump: {
      yes: {
        required: ['ASHP Proposal Summary', 'GASDD'],
      },
    },
    replaceDelivFuelWithHeatPump: {
      yes: {
        required: ['ASHP Proposal Summary'],
      },
    },
    recommendedHvac: {
      'DHW Only': {
        notRequired: ['Man J Full', 'Man J 1st'],
      },
      'Whole Home: DMS': {
        required: ['ASHP Proposal Summary'],
      },
      'Whole Home: CHP': {
        required: ['ASHP Proposal Summary'],
      },
      'Whole Home: CHP & DMS': {
        required: ['ASHP Proposal Summary'],
      },
      'Partial: DMS': {
        required: ['ASHP Proposal Summary'],
      },
    },
    electricalRecommended: {
      yes: {
        required: ['Electric Calc'],
      },
    },
  },
};

const documents = {
  'ASHP Proposal Summary': {
    fullName: 'Air Source Heat Pump Summary',
    fileType: ['pdf'],
  },
  Contract: {
    fullName: 'Contract',
    fileType: ['pdf'],
  },
  'Electric Calc': {
    fullName: 'Electric Calc',
    fileType: ['pdf'],
  },
  GASDD: {
    fullName: 'Gas Disclosure Displacement',
    fileType: ['pdf'],
  },
  'Man J Full': {
    fullName: 'Manual J - Full',
    fileType: ['pdf'],
  },
  'Man J 1st': {
    fullName: 'Manual J - 1st Page',
    fileType: ['pdf'],
  },
  PICS: {
    fullName: 'Pictures',
    fileType: ['pdf'],
  },
  PV: {
    fullName: 'Plan View',
    fileType: ['pdf'],
  },
  WS: {
    fullName: 'Work Sheet',
    fileType: ['xls', 'xlsm', 'xlsx', 'csv'],
  },
};

const existingHVACOptions = [
  'Gas Boiler',
  'Gas Furnace',
  'Deliverable Fuel Boiler',
  'Deliverable Fuel Furnace',
  'Electric Strips',
  'DMS/CHP',
  'Gas Space Heater',
  'Other',
];

const existingDHWOptions = [
  'Gas Atmospheric',
  'Gas Indirect',
  'Gas Power Vent',
  'Gas Tankless',
  'Oil Atmospheric',
  'Oil Indirect',
  'Oil Tankless',
  'Propane Atmospheric',
  'Electric Resistance',
  'HPHWH',
];

const recommendedHVACOptions = [
  'Whole Home: DMS',
  'Whole Home: CHP',
  'Whole Home: CHP & DMS',
  'Partial: DMS',
  'Boiler',
  'Furnace',
  'Gas Space Heater',
  'DHW Only',
];

const recommendedDHWOptions = [
  'No Change',
  'HPHWH',
  'Electric Resistance',
  'Gas Power Vented Tank',
  'Gas Indirect',
  'Gas Combi',
  'Chimney Liner',
];

export {
  resultingRequiredDocs,
  documents,
  existingHVACOptions,
  existingDHWOptions,
  recommendedHVACOptions,
  recommendedDHWOptions,
};
