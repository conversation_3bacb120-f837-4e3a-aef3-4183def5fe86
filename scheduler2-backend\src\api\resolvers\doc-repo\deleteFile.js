const moment = require('moment');
const docRepoModel = require('@models/docRepoModel');
const s3Model = require('@models/s3Model');
const config = require('@config/config');

const deleteFile = async (req, res, next) => {
  global.logger.debug('resolvers/doc-repo/deleteFile');
  global.logger.debug('with params: \n%o', { user: req.user, ...req.query });

  try {
    const operatorId = req.user._json.oid;
    const { doc, state, department, uniqueId } = req.query;
    const bucket = `${config.s3DocRepoPath}/${state}/${department.replace('-', '/')}/${uniqueId}`;
    const currentDateTime = moment().format('YYYY-MM-DDTHH:mm:ss');
    const newDocName = encodeURI(`deleted_${currentDateTime}_${doc}`);
    const formattedDoc = encodeURI(doc);
    const copyObjParams = {
      CopySource: `${bucket}/${formattedDoc}`,
      Bucket: bucket,
      Key: newDocName,
    };
    const deleteFileParams = {
      Bucket: bucket,
      Key: doc,
    };

    await s3Model.copyFile(copyObjParams);
    await s3Model.deleteFile(deleteFileParams);
    await docRepoModel.updateCAPVisitResult({ state, department, uniqueId });

    const reportObj = {
      oid: operatorId,
      action: 'delete',
      docName: doc,
      sfId: uniqueId,
      state,
      department,
    };
    await docRepoModel.docRepoReport(reportObj);

    return res.sendStatus(200);
  } catch (error) {
    return next(error);
  }
};

module.exports = deleteFile;
