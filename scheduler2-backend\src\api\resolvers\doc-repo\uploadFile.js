const moment = require('moment');
const docRepoModel = require('@models/docRepoModel');
const s3Model = require('@models/s3Model');
const config = require('@config/config');

const uploadFile = async (req, res, next) => {
  global.logger.debug('resolvers/doc-repo/uploadFile');
  global.logger.debug('with params: \n%o', {
    user: req.user,
    ...req.params,
    file: req.files.file.name,
  });

  try {
    const params = {
      file: req.files,
      ...req.params,
      author: req.user.displayName,
    };
    const operatorId = req.user._json.oid;
    const { file, state, uniqueId } = params;
    let { department } = params;
    department = department.replace('-', '/');
    const { name } = file.file;
    const bucket = `${config.s3DocRepoPath}/${state}/${department}/${uniqueId}`;
    const s3ObjectParams = {
      Bucket: bucket,
      Key: name,
    };
    const fileExists = await docRepoModel.doesFileExist(s3ObjectParams);
    // If the file exists we need to copy the file then delete it before uploading the new file
    if (fileExists) {
      const currentDateTime = moment().format('YYYY-MM-DDTHH:mm:ss');
      const s3CopyObjectParams = {
        CopySource: `${bucket}/${name}`,
        Bucket: bucket,
        Key: `deleted_${currentDateTime}_${name}`,
      };
      await s3Model.copyFile(s3CopyObjectParams);
      await s3Model.deleteFile(s3ObjectParams);
    }
    const response = await docRepoModel.uploadFile(params);
    await docRepoModel.updateCAPVisitResult({ state, department, uniqueId });

    const reportObj = {
      oid: operatorId,
      action: 'upload',
      docName: name,
      sfId: uniqueId,
      state,
      department,
    };
    await docRepoModel.docRepoReport(reportObj);

    return res.json(response);
  } catch (error) {
    return next(error);
  }
};

module.exports = uploadFile;
