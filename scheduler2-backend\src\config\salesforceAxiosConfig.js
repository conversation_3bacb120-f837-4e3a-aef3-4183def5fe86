const axios = require('axios');
const AWS = require('aws-sdk');

const s3 = new AWS.S3({
  s3ForcePathStyle: true,
});

const {
  salesforce: { baseUrl, servicesEndpoint, tokenEndpoint, s3ApplicationKey },
  salesforceUsername,
  salesforcePassword,
} = require('./config');

const errorCallback = (error) => {
  if (error) throw error;
};

let salesforceToken = null;
let salesforceAxiosInstance = null;

const generateAccessToken = async () => {
  const sfApplicationParams = {
    Bucket: 'salesforce-authentication',
    Key: s3ApplicationKey,
  };
  const applicationResponse = await s3.getObject(sfApplicationParams, errorCallback).promise();
  //create Oauth2 object
  const appInfo = JSON.parse(applicationResponse.Body.toString());
  const { client_secret: clientSecret, client_id: clientId } = appInfo;

  const config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };
  const params = new URLSearchParams();
  params.append('grant_type', 'password');
  params.append('client_id', clientId);
  params.append('client_secret', clientSecret);
  params.append('username', salesforceUsername);
  params.append('password', salesforcePassword);

  let accessToken;

  const tokenUrl = `${baseUrl}${tokenEndpoint}`;

  try {
    const { data: tokenData } = await axios.post(tokenUrl, params, config);

    ({ access_token: accessToken } = tokenData);
  } catch (error) {
    console.log(error.toJSON());
    throw Error('Failed Salesforce Authentication');
  }

  return accessToken;
};

const getSalesforceAxiosInstance = async () => {
  if (salesforceAxiosInstance) return salesforceAxiosInstance;

  // If there is no salesforce instance, generate a new token
  salesforceToken = await generateAccessToken();

  // Create salesforce instance
  salesforceAxiosInstance = axios.create({
    baseURL: `${baseUrl}${servicesEndpoint}`,
    method: 'POST',
    headers: {
      Authorization: `Bearer ${salesforceToken}`,
      'Content-Type': 'application/json',
    },
  });

  // Intercept error responses to check for 401 error
  salesforceAxiosInstance.interceptors.response.use(
    (response) => response,
    async (failedRequest) => {
      const status = failedRequest.response ? failedRequest.response.status : null;

      // If it is an 'unauthorized' error:
      if (status === 401) {
        // generate a new access token
        salesforceToken = await generateAccessToken();

        // update the stored axios instance
        salesforceAxiosInstance.defaults.headers.Authorization = `Bearer ${salesforceToken}`;

        // Delete the Authorization header on the old request since it has the bad access token
        delete failedRequest.config.headers.Authorization;

        // Re try the request with the new access token
        return salesforceAxiosInstance.request(failedRequest.config);
      }

      return Promise.reject(failedRequest);
    }
  );

  return salesforceAxiosInstance;
};

module.exports = { getSalesforceAxiosInstance };
