# Do not change version. This is the version of aws buildspec, not the version of your buldspec file.
version: 0.2
phases:
  pre_build:
    commands:
      - echo Installing dependencies, need webpack to run build...
      - npm install
  build:
    commands:
      - echo Build started on `date`
      - npm run build
  post_build:
    commands:
      - echo Build completed on `date`

artifacts:
  files:
    - dist/index.js
    - package.json
    - appspec.yml
    - run_pm2.sh
  discard-paths: yes
