const AWS = require('aws-sdk');
const { appEnv } = require('./config');
const environment = appEnv === 'production' ? 'prod' : 'dev';
const s3 = new AWS.S3({
  s3ForcePathStyle: true,
});
const jsforce = require('jsforce');

const keys = {
  application: {
    dev: 'sandbox/salesforce-client_secret.json',
    prod: 'production/salesforce-client_secret.json',
  },
  access: {
    dev: 'sandbox/SBXaccessTokenEncrypted',
    prod: 'production/PRODaccessTokenEncrypted',
  },
  refresh: {
    dev: 'sandbox/SBXrefreshTokenEncrypted',
    prod: 'production/PRODrefreshTokenEncrypted',
  },
};

const getSalesforceInstance = async (environment) => {
  const errorCallback = (error) => {
    if (error) throw error;
  };

  const sfApplicationParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.application[environment],
  };

  const applicationResponse = await s3.getObject(sfApplicationParams, errorCallback).promise();
  //create Oauth2 object
  const appInfo = JSON.parse(applicationResponse.Body.toString());
  const {
    client_secret: clientSecret,
    client_id: clientId,
    loginUrl,
    redirectUri,
    instanceUrl,
  } = appInfo;

  const oauth2 = new jsforce.OAuth2({
    loginUrl,
    clientId,
    clientSecret,
    redirectUri,
  });

  const sfAccessParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.access[environment],
  };

  const accessResponse = await s3.getObject(sfAccessParams, errorCallback).promise();
  const accessTokenParams = { CiphertextBlob: accessResponse.Body };

  const sfRefreshParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.refresh[environment],
  };

  const refreshResponse = await s3.getObject(sfRefreshParams, errorCallback).promise();
  const refreshTokenParams = { CiphertextBlob: refreshResponse.Body };

  const kms = new AWS.KMS({ region: 'us-east-1' });

  const { Plaintext: decryptedRefresh } = await kms
    .decrypt(refreshTokenParams, errorCallback)
    .promise();
  const { Plaintext: decryptedAccess } = await kms
    .decrypt(accessTokenParams, errorCallback)
    .promise();

  const accessToken = decryptedAccess.toString('ascii');
  const refreshToken = decryptedRefresh.toString('ascii');

  const salesforceConnection = new jsforce.Connection({
    oauth2,
    accessToken,
    refreshToken,
    instanceUrl,
  });

  salesforceConnection.on('error', errorCallback);

  global.logger.info(`Connected to ${environment} Salesforce`);
  return salesforceConnection;
};

const connectSalesforce = async () => {
  return getSalesforceInstance(environment);
};

const salesforceConnection = connectSalesforce();

module.exports = { salesforceConnection };
