const config = require('@config/config');
const axios = require('axios');

const fs = require('fs');
const AWS = require('aws-sdk');
const db = require('@config/databaseConnect');
const { getSalesforceAxiosInstance } = require('@config/salesforceAxiosConfig');
const removeEmptiesFromObject = require('../utils/removeEmptiesFromObject');
const { keysToUnderscoreCase } = require('../utils/camelCaseConverter');
const {
  prepareWorkReceiptFieldsForSalesforce,
  parseWorkReceiptFieldsForSalesforce,
} = require('../utils/parseWorkReceiptFieldsForSalesforce');
const { syncWorkReceiptFieldsToSF } = require('./salesforceModel');

const s3 = new AWS.S3({
  accessKeyId: config.awsAccessKeyId,
  secretAccessKey: config.awsAccessSecretKey,
});

const getWorkReceiptFromS3 = async ({ key, state, workReceiptType }) => {
  try {
    const bucket = config.s3WorkReceiptPath[config.appEnv];
    const fileParams = {
      Bucket: bucket,
      Key: `${state}/${workReceiptType}/${key}`,
    };
    const existingFile = await s3.getObject(fileParams).promise();
    const { Body: fileData, ContentType: fileType } = existingFile;
    return { fileData, fileType };
  } catch (error) {
    console.log(error);
    return false;
  }
};

const uploadWorkReceiptToS3 = async ({ fileData, fileType, state, workReceiptType }) => {
  try {
    const bucket = config.s3WorkReceiptPath[config.appEnv];
    const date = new Date();
    const formattedDate = date
      .toLocaleDateString('en-US')
      .split('/')
      .join('-');
    const formattedTime = date.toLocaleTimeString('en-US', { hour12: false });
    const folderPath = `${state}/${workReceiptType}/`;
    const archivedKeyPath = `archived-versions/${workReceiptType}-${formattedDate}-${formattedTime}.xlsm`;
    const archivedFilePath = `${folderPath}${archivedKeyPath}`;

    const uploadOldFileParams = {
      Bucket: bucket,
      Key: archivedFilePath,
      Body: fileData,
      ContentType: fileType,
    };

    return s3.putObject(uploadOldFileParams).promise();
  } catch (error) {
    console.log(error);
    return false;
  }
};

const deleteTemplateFromS3 = async (key) => {
  try {
    const bucket = config.s3WorkReceiptPath[config.appEnv];
    const deleteParams = {
      Bucket: bucket,
      Key: key,
    };
    await s3.deleteObject(deleteParams).promise();
  } catch (error) {
    console.log(error);
    return false;
  }
};

const uploadWorkReceiptTemplate = async (params) => {
  try {
    const templates = {
      CAP: 'CAPWRTemplate.xlsm',
      Offline: 'OfflineWRTemplate.xlsm',
      'Online-Single-Family': 'OnlineSingleFamilyWRTemplate.xlsm',
      'Online-Multi-Family': 'OnlineMultiFamilyWRTemplate.xlsm',
      'Offline-Multi-Family': 'OfflineMultiFamilyWRTemplate.xlsm',
    };

    const { file, workReceiptType, state } = params;
    const { path, type } = file.file;
    // eslint-disable-next-line no-sync
    const data = await fs.readFileSync(path);
    const bucket = config.s3WorkReceiptPath[config.appEnv];

    const template = await getWorkReceiptFromS3({
      key: templates[workReceiptType],
      state,
      workReceiptType,
    });

    if (template?.fileData && template?.fileType) {
      const { fileData, fileType } = template;
      await uploadWorkReceiptToS3({ fileData, fileType, state, workReceiptType });
      await deleteTemplateFromS3(templates[workReceiptType]);
    }

    const s3Params = {
      Bucket: bucket,
      Key: `${state}/${workReceiptType}/${templates[workReceiptType]}`,
      Body: data,
      ContentType: type,
    };
    const response = await s3.putObject(s3Params).promise();
    return response;
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const checkIfWorkReceiptExists = async (params) => {
  try {
    const dealId = `${params.slice(0, 15)}%`;
    const {
      rows: { 0: exists },
    } = await db.raw('SELECT 1 FROM workreceipt WHERE sf_deal_id like ?', dealId);
    if (exists) {
      return true;
    }
    return false;
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const generateWorkReceipt = async ({ data, mode, state }) => {
  try {
    const { appEnv, lambda } = config;
    const url =
      appEnv === 'production' ? lambda.generateWorkReceiptPath : lambda.generateWorkReceiptPathDev;
    const urlWithParams = `${url}?workReceiptMode=${mode}&state=${state}`;
    const response = await axios.post(urlWithParams, data, { headers: { env: appEnv } });
    return response?.data?.key;
  } catch (error) {
    console.log(error);
    error.params = data;
    throw error;
  }
};

const getHVACInstallProducts = async () => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = '/ProductRestService/';
    const data = {
      operation: 'getAllProducts',
    };
    const response = await salesforceAxiosInstance({ url, data });
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

const getAllProductsByOppId = async () => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = '/ProductRestService/';
    const data = {
      operation: 'getProducts',
      productRequests: [
        {
          opportunityId: '006U8000008MuuzIAC',
        },
      ],
    };
    const response = await salesforceAxiosInstance({ url, data });
    return response?.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

const deleteProductById = async (productId) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = '/ProductRestService/';
    const data = {
      operation: 'deleteProducts',
      productRequests: [
        {
          products: [
            {
              opportunityLineItemId: productId,
            },
          ],
        },
      ],
    };
    const response = await salesforceAxiosInstance({ url, data });
    return response?.data;
  } catch (error) {
    console.log('errr', error);
  }
};

const populateDealsDetailsFromUplight = async (params) => {
  try {
    const { appEnv, lambda } = config;
    const url =
      appEnv === 'production'
        ? lambda.populateDealFromUplightV2Path
        : lambda.populateDealFromUplightV2PathDev;
    const urlWithParams = `${url}?dealId=${params}`;
    const response = await axios.get(urlWithParams, {
      headers: {
        env: config.appEnv,
      },
    });

    return response.data;
  } catch (error) {
    if (error.response?.data) {
      throw new Error(error.response?.data);
    }
    throw error;
  }
};

// TODO: reconsider how this is working. It would be good not to use snake case in our javascript for convention's sake.
const saveWorkReceipt = async (workReceipt) => {
  try {
    const { rows } = await db.raw(
      ` SELECT column_name
      FROM information_schema.columns 
      WHERE table_name = 'workreceipt' `
    );
    const parsedWorkReceiptData = keysToUnderscoreCase(workReceipt);

    const columns = rows.map((row) => row.column_name);

    // Generate placeholders for parameterized values
    const placeholders = columns.map(() => '?').join(', ');

    for (let iterator = 0; iterator < parsedWorkReceiptData.length; iterator++) {
      // Prepare data for WR table Insertion/Update
      const isExists = await checkIfWorkReceiptExists(parsedWorkReceiptData[iterator].sf_deal_id);
      const values = removeEmptiesFromObject(parsedWorkReceiptData[iterator]);

      values.areas_on_workscope = JSON.stringify(values.areas_on_workscope);
      values.confirm_system_type = JSON.stringify(values.confirm_system_type);
      values.signs_of_k_t_multiselect = JSON.stringify(values.signs_of_k_t_multiselect);
      values.locations_of_k_t_multiselect = JSON.stringify(values.locations_of_k_t_multiselect);
      if (isExists) {
        const updateQuery = `
        UPDATE workreceipt
        SET ${columns.map((col) => `${col} = ?`).join(', ')}
        WHERE sf_deal_id = ?
      `;
        // Flatten the array of values to be passed as arguments
        const flattenedValues = columns.map((key) => values[key] || null);
        flattenedValues.push(values.sf_deal_id); // Add sf_deal_id for the WHERE clause

        // Execute the UPDATE query with the generated placeholders and flattened values
        await db.raw(updateQuery, flattenedValues);
      } else {
        // Generate the VALUES clause for the INSERT query
        const valuesClause = parsedWorkReceiptData.map(() => `(${placeholders})`).join(', ');

        // Construct the INSERT query string with column names and placeholders
        const insertQuery = `
        INSERT INTO workreceipt (${columns.join(', ')})
        VALUES ${valuesClause}
      `;
        // Flatten the array of values to be passed as arguments
        const flattenedValues = parsedWorkReceiptData.flatMap((obj) =>
          columns.map((key) => obj[key] || null)
        );

        // Execute the INSERT query with the generated placeholders and flattened values
        await db.raw(insertQuery, flattenedValues);
      }
      // Prepare Barrier Fields for Salesforce
      const { extraData } = prepareWorkReceiptFieldsForSalesforce(workReceipt[iterator]);
      // Parse Barrier Fields for Salesforce
      const {
        opportunityObject,
        sfObject,
        needsOpportunityUpdate,
        needsContractUpdate,
        contractObject,
      } = parseWorkReceiptFieldsForSalesforce({
        ...workReceipt[iterator],
        ...extraData,
        isMultiFamily: workReceipt.length > 1,
      });
      const { hvacInstallOpportunityId = '' } = workReceipt[iterator];

      // Remove Empty Nullish Falsy Values From Barrier SF Object
      const sfBarrierObject = removeEmptiesFromObject(sfObject);
      // Accounts Page
      const houseAge = new Date().getFullYear() - parseInt(sfBarrierObject.yearHouseBuilt, 10);
      const accountId = workReceipt[iterator].accountId;
      const preferredLanguage = workReceipt[iterator].preferredLanguage;

      const accountsObject = {
        Id: accountId,
        House_Age__c: houseAge,
        Preferred_Language__c: preferredLanguage,
      };
      // Update Deal, Account and Opportunity
      await syncWorkReceiptFieldsToSF({
        hvacInstallOpportunityId,
        opportunityObject,
        sfBarrierObject,
        needsOpportunityUpdate,
        accountId,
        needsContractUpdate,
        contractObject,
        accountsObject,
      });
    }
    return true;
  } catch (error) {
    console.log('error', error);
    error.params = { workReceipt };
    throw new Error(error);
  }
};

module.exports = {
  uploadWorkReceiptTemplate,
  saveWorkReceipt,
  populateDealsDetailsFromUplight,
  generateWorkReceipt,
  getAllProductsByOppId,
  deleteProductById,
  getHVACInstallProducts,
};
