const axios = require('axios');
const config = require('@config/config');
const salesforceModel = require('@models/salesforceModel');
const { keysToCamelCase, keysToUnderscoreCase } = require('@utils/camelCaseConverter');

const fs = require('fs');
const AWS = require('aws-sdk');
const moment = require('moment');
const db = require('@config/databaseConnect');

const s3 = new AWS.S3({
  accessKeyId: config.awsAccessKeyId,
  secretAccessKey: config.awsAccessSecretKey,
});

// Upload File
const uploadFile = async (params) => {
  const { file, state, uniqueId, author } = params;
  let { department } = params;
  department = department.replace('-', '/');
  const { path, name, type } = file.file;
  try {
    // eslint-disable-next-line no-sync
    const data = await fs.readFileSync(path);
    const s3Params = {
      Bucket: `${config.s3DocRepoPath}/${state}/${department}/${uniqueId}`,
      Key: name,
      Body: data,
      ContentType: type,
      Metadata: {
        author,
      },
    };
    const response = await s3.putObject(s3Params).promise();
    return response;
  } catch (error) {
    error.params = params;
    throw error;
  }
};

// Upload required document
const uploadFileRename = async (params) => {
  const { file, state, department, uniqueId, newName } = params;
  const { path, type } = file.file;
  try {
    // eslint-disable-next-line no-sync
    const data = await fs.readFileSync(path);
    const s3Params = {
      Bucket: `${config.s3DocRepoPath}/${state}/${department}/${uniqueId}`,
      Key: newName,
      Body: data,
      ContentType: type,
    };
    const response = await s3.putObject(s3Params).promise();
    return response;
  } catch (error) {
    error.params = params;
    throw error;
  }
};

// Download File from S3.
const downloadFile = async (s3BucketParams) => {
  try {
    const response = await s3.getObject(s3BucketParams).promise();
    return response;
  } catch (error) {
    error.params = s3BucketParams;
    return error;
  }
};

// Retrieve sensitive documents by department from sensitive_documents table.
const getSensitiveDocsByDepartment = async (department) => {
  try {
    const { rows } = await db.raw('SELECT document FROM sensitive_documents WHERE department = ?', [
      department,
    ]);
    return rows;
  } catch (error) {
    error.params = department;
    throw error;
  }
};

// Remove sensitive documents from uploaded files from external partners.
const removeSensitiveDocs = (files, sensitiveDocs) => {
  const filesCopy = { ...files };
  sensitiveDocs.forEach((docObj) => {
    if (filesCopy[docObj.document]) delete filesCopy[docObj.document];
  });
  return filesCopy;
};

// Get all documents from S3.
const getAllDocuments = async (params) => {
  const { isExternal } = params;
  let { bucketKeys } = params;
  bucketKeys = bucketKeys.replace('-', '/');
  const uniqueId = bucketKeys.split('/')[3];
  let { department } = params;
  department = department.replace('-', '/');
  const s3Params = {
    Bucket: department === '' ? config.sch1DocRepoPath : config.s3DocRepoPath,
    Prefix: bucketKeys,
  };
  try {
    let appliancePerformed = null;
    if (department.includes('HEA') && !bucketKeys.startsWith('CT/'))
      ({ appliancePerformed } = await salesforceModel.getHEAEventInfoWithDealId(uniqueId, true));
    let resulting = { appliancePerformed };
    // If user is external, need to get the list of sensitive documents from database to remove from uploaded files.
    const sensitiveDocs = isExternal ? await getSensitiveDocsByDepartment(department) : [];
    // Contents is an array of documents already uploaded to S3. They come back as URLs.
    const { Contents } = await s3.listObjectsV2(s3Params).promise();
    const fileObj = {};
    if (Contents.length) {
      // Uploaded documents come back as URLs. The last param in the url is the document name.
      for (let i = 0; i < Contents.length; i++) {
        const doc = Contents[i];
        const fileName = doc.Key.split('/').pop();
        if (!fileName.startsWith('deleted_') && fileName.length > 0) {
          /*
            This checked object is added to every file for the checkbox in the frontend.
            Not sure on another way to handle checkboxes. I could add this in the front.
            Not sure on the best approach so for now it will be here in the backend.
          */
          const metadata = await getObjectDetails({
            Bucket: config.s3DocRepoPath,
            Key: doc.Key,
          });
          const { LastModified, Metadata } = metadata;
          if (fileName.includes('resulting')) {
            const formattedMetadata = keysToCamelCase(Metadata);
            resulting = { ...resulting, ...formattedMetadata, uniqueId };
            continue;
          }
          fileObj[fileName] = {
            checked: false,
            docUploadedTimeStamp: moment(LastModified).format('MM-DD-YYYY hh:mm a'),
            author: Metadata.author,
          };
        }
      }
    }
    // Front-end needs to know which documents are already uploaded before deleting sensitive documents.
    const alreadyUploadedDocs = Object.keys(fileObj);
    // Remove sensitive documents from the uploaded files if user is external.
    const finalDocuments =
      isExternal && sensitiveDocs.length ? removeSensitiveDocs(fileObj, sensitiveDocs) : fileObj;
    return {
      finalDocuments,
      alreadyUploadedDocs,
      resulting,
    };
  } catch (error) {
    error.params = params;
    throw error;
  }
};

// Get single file for view file
const doesFileExist = async (s3GetObjectParams) => {
  try {
    await s3.getObject(s3GetObjectParams).promise();
    return true;
  } catch (error) {
    return false;
  }
};

// Report it!
const docRepoReport = async (reportObj) => {
  const { oid, action, docName, sfId, state, department } = reportObj;
  const timeNow = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
  try {
    await db.raw(
      'INSERT INTO doc_repo_reporting (oid, action, docname, sf_unique_id, state, department, timestamp) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [oid, action, docName, sfId, state, department, timeNow]
    );
  } catch (error) {
    error.params = { reportObj };
    throw error;
  }
};

const erbImages = async (dealId, origin) => {
  // Change Bucket to point to doc-repo
  // Change hardcoded dealId with dealId params.
  let bucket = 'doc-repo';
  if (origin !== 'https://sch.homeworksenergy.com') bucket += '-test';

  // Test Prefix : `a0h0y00000Mn0R6/`
  const s3Params = {
    Bucket: bucket,
    Prefix: dealId,
  };

  try {
    const { Contents: allDocsUploadedToS3 } = await s3.listObjectsV2(s3Params).promise();
    const filteredDocs = allDocsUploadedToS3.filter((doc) => {
      if (doc.Key.includes('EBRImage') && !doc.Key.includes('deleted')) return true;
      return false;
    });
    const documentKeysArr = [];
    filteredDocs.forEach((doc) => documentKeysArr.push(doc.Key));
    return documentKeysArr;
  } catch (error) {
    error.params = { dealId };
    throw error;
  }
};

const getDocumentUrl = async ({ Bucket, Key }) => {
  try {
    const url = await s3.getSignedUrl('getObject', {
      Bucket,
      Key,
      Expires: 300,
    });
    return url;
  } catch (error) {
    error.params = { Bucket, Key };
    throw error;
  }
};

const getObjectListByBucket = async (s3Params) => {
  try {
    const { Contents } = await s3.listObjectsV2(s3Params).promise();
    return Contents;
  } catch (error) {
    error.params = { s3Params };
    throw error;
  }
};

const getObjectDetails = async (s3Params) => {
  try {
    const objMetadata = await s3.headObject(s3Params).promise();
    return objMetadata;
  } catch (error) {
    error.params = { s3Params };
    throw error;
  }
};

const getPartnerDocs = async ({ state, department, uniqueId, dealId }) => {
  try {
    const shortNameDict = {
      PWB: 'PreWx Barrier Incentive (PWB)',
      PICS: 'Pictures (PICS)',
      PV: 'Plan View (PV)',
      MOLD: 'Mold (MOLD)',
    };
    const sensitiveDocs = (await getSensitiveDocsByDepartment(department)) || [];

    const docs = [];
    let partnerSensitiveDocsBucket = `${config.s3DocRepoPath}/${state}/${department}/${dealId}`;
    if (department === 'Partners')
      partnerSensitiveDocsBucket = `${config.sch1DocRepoPath}/${dealId.slice(0, 15)}`;

    for (let i = 0; i < sensitiveDocs.length; i++) {
      const { document } = sensitiveDocs[i];
      const s3ObjectParams = {
        Bucket: partnerSensitiveDocsBucket,
        Key: document,
      };
      let fileExists = await doesFileExist(s3ObjectParams);
      if (!fileExists) {
        fileExists = await doesFileExist({
          Bucket: `${config.s3DocRepoPath}/${state}/HEA/CAP/${dealId}`,
          Key: document,
        });
      }

      const [docName, type] = document.split('.');

      const docDetail = {
        name: shortNameDict[docName],
        sensitive: true,
        uploaded: fileExists,
        type,
        fileName: docName,
      };
      docs.push(docDetail);
    }

    const s3Params = {
      Bucket: config.s3DocRepoPath,
      Prefix: `${state}/${department}/${uniqueId}`,
    };

    const { Contents } = await s3.listObjectsV2(s3Params).promise();

    if (Contents.length) {
      // Uploaded documents come back as URLs. The last param in the url is the document name.
      for (let i = 0; i < Contents.length; i++) {
        const doc = Contents[i];
        const fileName = doc.Key.split('/').pop();
        if (!fileName.startsWith('deleted_') && fileName.length > 0) {
          const [docName, type] = fileName.split('.');
          const metadata = await getObjectDetails({
            Bucket: config.s3DocRepoPath,
            Key: doc.Key,
          });
          const {
            LastModified,
            Metadata: { author },
          } = metadata;
          const docDetail = {
            name: docName,
            sensitive: false,
            uploaded: true,
            type,
            fileName,
            uploadDate: moment(LastModified).format('MM-DD-YYYY'),
            uploadedBy: author,
          };
          docs.push(docDetail);
        }
      }
    }
    return { docs };
  } catch (error) {
    error.params = { state, department, uniqueId, dealId };
    throw error;
  }
};

const updateResulting = async (params) => {
  const { state, department, uniqueId, ...otherParams } = params;
  let metadataParams = otherParams;
  const formattedDept = department.replace('-', '/');
  try {
    if (department === 'HEA-CAP') {
      const {
        sfObject: { Mold_Remediation_Contractor_Choice__c: moldRemediationContractorChoice },
      } = await salesforceModel.getObject('Deal__c', uniqueId);
      metadataParams.moldRemediationContractorChoice =
        moldRemediationContractorChoice === 'Quote Developed in Home' ? 'yes' : 'no';
      await salesforceModel.updateObjects('Deal__c', {
        Id: uniqueId,
        LTA_Waiver_Status__c: otherParams.lta === 'yes' ? 'Received' : 'Missing',
      });
    } else if (department === 'HVAC_Sales-CAP') {
      metadataParams = await getHvacSalesCapReqDocs(uniqueId, metadataParams);
    }
    const formattedResulting = keysToUnderscoreCase(metadataParams);
    const s3Params = {
      Bucket: `${config.s3DocRepoPath}/${state}/${formattedDept}/${uniqueId}`,
      Key: 'resulting',
      Metadata: {
        ...formattedResulting,
      },
    };
    await s3.putObject(s3Params).promise();
    const metadata = await getObjectDetails({
      Bucket: config.s3DocRepoPath,
      Key: `${state}/${formattedDept}/${uniqueId}/resulting`,
    });
    const { Metadata } = metadata;
    return keysToCamelCase({ ...Metadata, uniqueId });
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const getHvacSalesCapReqDocs = async (dealId, params) => {
  const heaEventDetails = await salesforceModel.getHEAEventInfoWithDealId(dealId);
  const {
    sfIds: { opportunityId },
  } = heaEventDetails;
  if (opportunityId) {
    const opportunityDetails = await salesforceModel.getObject('Opportunity', opportunityId);
    const {
      sfObject: { Electrical_Recommended__c: electricalRecommended },
    } = opportunityDetails;
    if (electricalRecommended !== null && electricalRecommended !== 'No Change') {
      params.electricalRecommended = 'yes';
      params.requiredDocs = `${params.requiredDocs},Electric Calc`;
    } else {
      params.electricalRecommended = 'no';
      params.requiredDocs = params.requiredDocs
        .split(',')
        .filter((doc) => doc !== 'Electric Calc')
        .join(',');
    }
    return params;
  }
};

const mergeDocuments = async (params) => {
  try {
    const lambdaStage = config.appEnv === 'staging' ? 'development' : config.appEnv;
    await axios.post(`${config.lambda.hweServiceHub}/${lambdaStage}/mergeDocuments`, params);
    return;
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const updateCAPVisitResult = async ({ state, department, uniqueId }) => {
  const isCAP = department.includes('CAP');
  const isHVACCAP = department.includes('HVAC');
  const isHEACAP = department.includes('HEA');
  if (isCAP) {
    const s3BucketParams = {
      bucketKeys: `${state}/${department}/${uniqueId}`,
      isExternal: false,
      department,
    };
    const {
      alreadyUploadedDocs,
      resulting,
      resulting: {
        visitResult,
        requiredDocs,
        recommendedHvac,
        recommendedDhw,
        existingHvac,
        existingDhw,
      },
    } = await getAllDocuments(s3BucketParams);
    const alreadyUploadedFormattedDocs = alreadyUploadedDocs.map((doc) => {
      return doc.split('.')[0];
    });
    const requiredDocsArr = requiredDocs?.split(',') || [];
    const uploadedRequiredDocs = alreadyUploadedFormattedDocs.filter((alreadyUplodedDoc) =>
      requiredDocsArr.some((reqDoc) => reqDoc === alreadyUplodedDoc)
    );
    const isRequiredDocsUploaded = requiredDocsArr.length === uploadedRequiredDocs.length;

    let updateFields = isHVACCAP ? { Interested_in_HVAC__c: null } : { docRepoStatus__c: null };
    let hvacInstallOpportunity = null;
    if (requiredDocsArr.length === uploadedRequiredDocs.length) {
      const resultingDict = {
        'Closed Won': '1',
        'High Prob': '2',
        'Qualified Out': '3',
        'Pre-Weatherization Barrier': '4',
        'Not in EM Home': '5',
      };
      if (isHEACAP) {
        const largeApplianceRecommended = [];
        const largeAppliancesRecommendedDict = {
          recommendFridge: 'Fridge',
          recommendFreezer: 'Freezer',
          recommendWasher: 'Washer',
          recommendDehumidifier: 'Dehumidifier',
          recommendWindowAcUnit: 'Window AC',
        };
        Object.keys(largeAppliancesRecommendedDict).forEach((appliance) => {
          if (resulting[appliance] === 'yes')
            largeApplianceRecommended.push(largeAppliancesRecommendedDict[appliance]);
        });
        updateFields.Appliances_Recommended__c =
          largeApplianceRecommended.length === 0 ? 'None' : largeApplianceRecommended.join(';');
      }
      if (isHVACCAP) {
        updateFields.Interested_in_HVAC__c = isRequiredDocsUploaded ? 'Interested' : '';
        const accountId = await salesforceModel.getAccountIdWithDealId(uniqueId);
        const opportunities = await salesforceModel.getOpportunityInfoWithAccountId(accountId);
        if (opportunities.length > 0) {
          hvacInstallOpportunity = opportunities.find(({ Name }) => {
            return Name.toLowerCase().includes('hvac install');
          });
        }
      } else updateFields.docRepoStatus__c = resultingDict[visitResult];
    }
    updateFields = await salesforceModel.updateObjects('Deal__c', {
      Id: uniqueId,
      ...updateFields,
    });
    if (hvacInstallOpportunity?.Id)
      await salesforceModel.updateObjects('Opportunity', {
        Id: hvacInstallOpportunity.Id,
        Recommended_HVAC__c: recommendedHvac,
        Recommended_DHW__c: recommendedDhw,
        Existing_DHW__c: existingDhw,
        Existing_HVAC__c: existingHvac,
      });
  }
};

module.exports = {
  uploadFile,
  getAllDocuments,
  downloadFile,
  doesFileExist,
  uploadFileRename,
  docRepoReport,
  erbImages,
  getDocumentUrl,
  getObjectListByBucket,
  getObjectDetails,
  getPartnerDocs,
  updateResulting,
  mergeDocuments,
  updateCAPVisitResult,
};
