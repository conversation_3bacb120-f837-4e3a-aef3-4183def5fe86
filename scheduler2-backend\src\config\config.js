const {
  NODE_ENV,
  APP_ENV,
  AZURE_CLIENT_ID,
  AZURE_CLIENT_SECRET,
  AZURE_REDIRECT_URL,
  GOOGLE_API_KEY,
  MAPQUEST_API_KEY,
  LAMBDA_PASSWORD,
  DB_CONNECTION_STRING,
  REDIS_CONNECTION_STRING,
  DEV_USERNAME,
  DEV_OID,
  SESSION_SECRET,
  DOMAIN,
  PORT,
  AWS_ACCESS_KEY_ID,
  AWS_ACCESS_SECRET_KEY,
  SALESFORCE_USERNAME,
  SALESFORCE_PASSWORD,
  SALESFORCE_2_USERNAME,
  SALESFORCE_2_PASSWORD,
} = process.env;

const isProd = APP_ENV === 'production';

const salesforce = {
  baseUrl: isProd
    ? 'https://homeworks.my.salesforce.com/'
    : 'https://homeworks--uatsandbox.sandbox.my.salesforce.com/',
  s3ApplicationKey: isProd
    ? 'production/salesforce-client_secret.json'
    : 'sandbox/salesforce-client_secret.json',
  s3AccessKey: isProd ? 'production/PRODaccessTokenEncrypted' : 'sandbox/SBXaccessTokenEncrypted',
  s3RefreshKey: isProd
    ? 'production/PRODrefreshTokenEncrypted'
    : 'sandbox/SBXrefreshTokenEncrypted',
  tokenEndpoint: 'services/oauth2/token',
  tokenIntrospectEndpoint: 'services/oauth2/introspect',
  servicesEndpoint: 'services/apexrest/',
};

const newSalesforce = {
  baseUrl: isProd
    ? 'https://hwe.my.salesforce.com/'
    : 'https://hwe--uat.sandbox.my.salesforce.com/',
  s3ApplicationKey: isProd
    ? 'production/salesforce-client_secret.json'
    : 'sandbox/salesforce-client_secret.json',
  s3AccessKey: isProd ? 'production/PRODaccessTokenEncrypted' : 'sandbox/SBXaccessTokenEncrypted',
  s3RefreshKey: isProd
    ? 'production/PRODrefreshTokenEncrypted'
    : 'sandbox/SBXrefreshTokenEncrypted',
  tokenEndpoint: 'services/oauth2/token',
  tokenIntrospectEndpoint: 'services/oauth2/introspect',
  servicesEndpoint: 'services/apexrest/',
};

let bundleJsPath = 'http://localhost:8083';
const s3WorkReceiptPath = {
  production: 'workreceipt',
  development: 'workreceipt-test',
};
let s3DocRepoPath = 'doc-repo-v2-test';
let financePayrollBucketName = 'finance-pr-test';
let sch1DocRepoPath = 'doc-repo-test';
let dynamoDbTable = 'hwe-docs-test';
if (APP_ENV === 'production') {
  bundleJsPath = 'https://s3.amazonaws.com/hwe-static-assets/scheduler2/production';
  s3DocRepoPath = 'doc-repo-v2';
  sch1DocRepoPath = 'doc-repo';
  financePayrollBucketName = 'finance-pr';
  dynamoDbTable = 'hwe-docs';
}
if (APP_ENV === 'staging') {
  bundleJsPath = 'https://s3.amazonaws.com/hwe-static-assets/scheduler2/testing';
}

const config = {
  env: NODE_ENV,
  appEnv: APP_ENV,
  devUser: {
    displayName: DEV_USERNAME,
    oid: DEV_OID,
  },
  bundleJsPath,
  server: {
    domain: DOMAIN,
    port: PORT,
    sessionSecret: SESSION_SECRET,
  },
  db: {
    connectionString: DB_CONNECTION_STRING,
  },
  google: {
    apiKey: GOOGLE_API_KEY,
    baseUrl: 'https://maps.googleapis.com/maps/api/',
  },
  mapquest: {
    apiKey: MAPQUEST_API_KEY,
    baseUrl: 'http://www.mapquestapi.com/directions/v2/',
  },
  lambda: {
    password: LAMBDA_PASSWORD,
    generateWorkReceiptPath:
      'https://7omz2t4hwstkwu3mcpkmbzliby0qzmbi.lambda-url.us-east-1.on.aws/',
    generateWorkReceiptPathDev:
      'https://5z2rp4fziaqdizbkk2wlmdsw5i0wkwae.lambda-url.us-east-1.on.aws/',
    hweServiceHub: 'https://mfw2snvqs7.execute-api.us-east-1.amazonaws.com',
    populateDealFromUplightV2Path:
      'https://2j7me37dr6cdz4bg67yhhzp7km0nvmeq.lambda-url.us-east-1.on.aws/',
    populateDealFromUplightV2PathDev:
      'https://km4zg5un6ca3oexu6gxabr6zxa0drsob.lambda-url.us-east-1.on.aws/',
    createBarrierApiPath: 'https://x29nsslalb.execute-api.us-east-1.amazonaws.com',
    emailDocs: 'https://x29nsslalb.execute-api.us-east-1.amazonaws.com',
  },
  redis: {
    connectionString: REDIS_CONNECTION_STRING,
  },
  azure: {
    redirectUrl: AZURE_REDIRECT_URL,
    allowHttpForRedirectUrl: true,
    identityMetadata: 'https://login.microsoftonline.com/common/.well-known/openid-configuration', // For using Microsoft you should never need to change this.
    clientID: AZURE_CLIENT_ID,
    clientSecret: AZURE_CLIENT_SECRET, // if you are doing code or id_token code
    skipUserProfile: true, // for AzureAD should be set to true.
    responseType: 'id_token code', // for login only flows use id_token. For accessing resources use `id_token code`
    responseMode: 'form_post', // For login only flows we should have token passed back to us in a POST
  },
  awsAccessKeyId: AWS_ACCESS_KEY_ID,
  awsAccessSecretKey: AWS_ACCESS_SECRET_KEY,
  s3DocRepoPath,
  s3WorkReceiptPath,
  sch1DocRepoPath,
  financePayrollBucketName,
  dynamoDbTable,
  salesforce,
  newSalesforce,
  salesforceUsername: SALESFORCE_USERNAME,
  salesforcePassword: SALESFORCE_PASSWORD,
  salesforceTwoUsername: SALESFORCE_2_USERNAME,
  salesforceTwoPassword: SALESFORCE_2_PASSWORD,
};

module.exports = config;
