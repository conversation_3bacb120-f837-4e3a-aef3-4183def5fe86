const config = require('@config/config');
const { types } = require('pg');

// Change type parser to return floats instead of strings for numeric the database
types.setTypeParser(types.builtins.NUMERIC, (value) => {
  return parseFloat(value);
});

let knex = require('knex');

knex = knex({
  client: 'pg',
  connection: config.db.connectionString,
  searchPath: ['knex', 'public'],
  pool: {
    min: 0,
    max: 25,
  },
});

// Wrapper function to allow a KNEX query to use or not use a transaction
// Useful for model functions that may not always have multiple calls associated
knex.optionalTransacting = (call, trx) => {
  if (trx) return call.transacting(trx);
  return call;
};

module.exports = knex;
