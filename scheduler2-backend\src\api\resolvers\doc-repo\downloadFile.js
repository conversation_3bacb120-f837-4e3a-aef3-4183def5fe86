const docRepoModel = require('@models/docRepoModel');
const eventsModel = require('@models/eventsModel');
const config = require('@config/config');

const downloadFile = async (req, res, next) => {
  global.logger.debug('resolvers/doc-repo/downloadFile');
  global.logger.debug('with params: \n%o', { user: req.user, ...req.query });
  try {
    const operatorId = req.user._json.oid;
    const { doc, state, department, uniqueId } = req.query;
    let s3Bucket = `${config.s3DocRepoPath}/${state}/${department.replace('-', '/')}/${uniqueId}`;
    if (department === 'Partners' && ['PICS.pdf', 'PWB.pdf'].includes(doc))
      s3Bucket = `${config.sch1DocRepoPath}/${uniqueId.slice(0, 15)}`;
    const s3BucketParams = {
      Bucket: s3Bucket,
      Key: doc,
    };
    let response = await docRepoModel.downloadFile(s3BucketParams);
    let fileExists = true;
    if (response?.statusCode === 404 && department === 'HVAC_Install') {
      const bucketParams = [];
      const searchResults = await eventsModel.searchEvents({
        searchTerm: uniqueId,
        departmentEventTypes: ['0001'],
        fieldsToSearch: ['type'],
      });
      if (searchResults.length > 0) {
        const [{ sfIds }] = searchResults;
        bucketParams.push({
          Bucket: `${config.s3DocRepoPath}/${state}/HVAC_Sales/${sfIds.opportunityId}`,
          Key: doc,
        });
      }
      for (let i = 0; i < bucketParams.length; i++) {
        fileExists = await docRepoModel.doesFileExist(bucketParams[i]);
        if (fileExists) {
          response = await docRepoModel.downloadFile(bucketParams[i]);
          break;
        }
      }
    }
    const reportObj = {
      oid: operatorId,
      action: 'download',
      docName: doc,
      sfId: uniqueId,
      state,
      department,
    };
    await docRepoModel.docRepoReport(reportObj);

    return res.json(response);
  } catch (error) {
    return next(error);
  }
};

module.exports = downloadFile;
