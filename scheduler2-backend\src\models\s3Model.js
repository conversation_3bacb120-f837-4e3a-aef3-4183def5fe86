const config = require('@config/config');
const AWS = require('aws-sdk');

const s3 = new AWS.S3({
  accessKeyId: config.awsAccessKeyId,
  secretAccessKey: config.awsAccessSecretKey,
});

// Delete File
// After copying file over we will delete original file.
const deleteFile = async (s3DeleteParams) => {
  try {
    await s3.deleteObject(s3DeleteParams).promise();
    return { success: true };
  } catch (error) {
    error.params = s3DeleteParams;
    throw error;
  }
};

// Copy file to rename deleted_timeStamp_fileName
const copyFile = async (s3CopyObjectParams) => {
  try {
    const response = await s3.copyObject(s3CopyObjectParams).promise();
    return response;
  } catch (error) {
    error.params = s3CopyObjectParams;
    throw error;
  }
};

module.exports = {
  deleteFile,
  copyFile,
};
